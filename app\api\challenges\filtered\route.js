import { NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

export async function GET(request) {
  try {
    // Verifica che l'utente sia autenticato
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Non autorizzato" },
        { status: 401 }
      );
    }

    // Ottieni i parametri di query
    const { searchParams } = new URL(request.url);
    const filter = searchParams.get('filter') || 'active'; // active, completed, archived

    // Ottieni l'ID dell'utente dal database
    const { data: userData, error: userError } = await supabase
      .from("app_users")
      .select("id")
      .eq("email", session.user.email)
      .single();

    if (userError || !userData) {
      console.error("Errore nel recupero dell'utente:", userError);
      return NextResponse.json(
        { error: "Utente non trovato" },
        { status: 404 }
      );
    }

    // Costruisci la query base
    let query = supabase
      .from("saved_challenges")
      .select("*")
      .eq("user_id", userData.id);

    // Applica i filtri - semplifichiamo la logica e facciamo il filtraggio lato client
    switch (filter) {
      case 'completed':
        // Per le completate, prendiamo solo quelle non archiviate
        query = query.eq('archived', false);
        break;

      case 'archived':
        // Sfide archiviate - prendiamo tutte quelle archiviate
        query = query.eq('archived', true);
        break;

      case 'active':
      default:
        // Per le attive, prendiamo solo quelle non archiviate
        query = query.eq('archived', false);
        break;
    }

    // Ordina per data di creazione (più recenti prima)
    query = query.order("created_at", { ascending: false });

    const { data, error } = await query;

    if (error) {
      console.error("Errore nel recupero delle sfide:", error);
      return NextResponse.json(
        { error: "Errore nel recupero delle sfide" },
        { status: 500 }
      );
    }

    // Filtra i risultati lato client in base al tipo richiesto
    let filteredData = data;

    if (filter === 'completed') {
      // Sfide completate: tutti e 3 gli obiettivi sono TRUE e non archiviate
      filteredData = data.filter(challenge =>
        !challenge.archived &&
        challenge.objective_completed === true &&
        challenge.squad_completed === true &&
        challenge.tactics_completed === true
      );
    } else if (filter === 'active') {
      // Sfide attive: almeno uno dei 3 obiettivi è FALSE e non archiviate
      filteredData = data.filter(challenge =>
        !challenge.archived &&
        (challenge.objective_completed === false ||
         challenge.squad_completed === false ||
         challenge.tactics_completed === false)
      );
    }
    // Per 'archived' non serve filtraggio aggiuntivo, la query già filtra

    return NextResponse.json(
      { challenges: filteredData },
      { status: 200 }
    );
  } catch (error) {
    console.error("Errore nel recupero delle sfide:", error);
    return NextResponse.json(
      { error: "Errore nel recupero delle sfide" },
      { status: 500 }
    );
  }
}
